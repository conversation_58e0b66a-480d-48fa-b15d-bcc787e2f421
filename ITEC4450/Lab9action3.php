<?php
    session_start();
    error_reporting(E_ERROR | E_PARSE);
?>

<html>
    <head>
        <title>Lab 9</title>
    </head>

    <body>
        <h1>Thank you for your payment</h1>

        <?php
            echo "Totally $".$_SESSION["totalCost"]." has been deducted from your credit card: ".$_SESSION["card_number"]."<br>";
            echo "The following item(s) will be shipped to the following address: ".$_SESSION["shipping_address"]."<br>";

            echo "<hr>";
            echo "<table align='center'>";
            echo "<tr>  <th>Item</th> <th>Price</th> <th>Quantity</th>  <th>Cost</th> </tr>";
            foreach ($_SESSION["buy"] as $item)
            {
                $itemCost = $item[2] * $item[3];
                echo "<tr>";
                echo "<td> <img src='" . $item[1] . "' width='100' height='100'> </td>";
                echo "<td> $" . $item[2] . "</td>";
                echo "<td>" . $item[3] . "</td>";
                echo "<td> $" . $itemCost . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<hr>";

            echo "Your purchase transaction is done. Please click <a href='Lab9.php'>here</a> to buy more!";

            session_unset();
            session_destroy();
        ?>
    </body>
</html>