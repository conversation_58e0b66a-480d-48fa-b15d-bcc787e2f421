<?php
    session_start();
?>

<html>
    <head>
        <title>Lab 9</title>
    </head>

    <body>
        <?php
            echo "Items you selected to purchase: <br>";

            echo "<hr>";
            if (isset($_POST["submit"]))
            {
                foreach ($_POST["want"] as $index=>$whichItem)
                {
                    if (isset($_SESSION["buy"]["$whichItem"]))
                    {
                        $_SESSION["buy"]["$whichItem"] = array($_POST["name"][$index], $_POST["image"][$index],
                            $_POST["price"][$index], $_POST["amount"][$index] + $_SESSION["buy"]["$whichItem"][3]);
                    }
                    else
                        $_SESSION["buy"]["$whichItem"] = array($_POST["name"][$index], $_POST["image"][$index],
                            $_POST["price"][$index], $_POST["amount"][$index]);
                }
            }

            if (isset($_SESSION["buy"]) && !empty($_SESSION["buy"]))
            {
                $_SESSION["totalCost"] = 0;
                echo "<table align='center'>";
                echo "<tr>  <th>Item</th> <th>Price</th> <th>Quantity</th>  </tr>";
                echo "<tr>";

                foreach ($_SESSION["buy"] as $item)
                {
                    $_SESSION["totalCost"] += $item[2] * $item[3];
                    echo "<tr>";
                    echo "<td> <img src='" . $item[1] . "' width='100' height='100'> </td>";
                    echo "<td> $" . $item[2] . "</td>";
                    echo "<td>" . $item[3] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "<hr>";
                echo "The total cost is \$" . $_SESSION["totalCost"] . "<br>";

                echo "<a href='Lab9action2.php'>Check Out</a> or <a href='Lab9.php'>Continue Shopping</a>";
            }
        ?>
    </body>
</html>